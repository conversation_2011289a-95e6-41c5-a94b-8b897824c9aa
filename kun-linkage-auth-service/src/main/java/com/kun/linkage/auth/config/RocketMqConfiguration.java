package com.kun.linkage.auth.config;

import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.PropertyAccessor;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import org.apache.rocketmq.spring.autoconfigure.RocketMQAutoConfiguration;
import org.apache.rocketmq.spring.core.RocketMQTemplate;
import org.apache.rocketmq.spring.support.RocketMQMessageConverter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.AutoConfigureAfter;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.messaging.converter.CompositeMessageConverter;
import org.springframework.messaging.converter.MappingJackson2MessageConverter;
import org.springframework.messaging.converter.MessageConverter;

import javax.annotation.PostConstruct;
import java.util.List;

/**
 * RocketMQ配置类
 * <p>
 * 主要用于配置Jackson序列化器，支持Java 8日期时间类型
 */
@Configuration
@AutoConfigureAfter(RocketMQAutoConfiguration.class)
public class RocketMqConfiguration {

    @Autowired(required = false)
    private RocketMQTemplate rocketMQTemplate;

    /**
     * 创建自定义的ObjectMapper Bean，确保在RocketMQ初始化前就可用
     */
    @Bean
    @Primary
    public ObjectMapper objectMapper() {
        ObjectMapper objectMapper = new ObjectMapper();

        // 注册JavaTimeModule，支持Java 8日期时间类型
        objectMapper.registerModule(new JavaTimeModule());

        // 禁用将日期写为时间戳
        objectMapper.disable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS);

        // 配置反序列化时忽略未知属性
        objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        objectMapper.configure(DeserializationFeature.ACCEPT_EMPTY_STRING_AS_NULL_OBJECT, true);

        // 设置可见性
        objectMapper.setVisibility(PropertyAccessor.ALL, JsonAutoDetect.Visibility.ANY);

        // 配置序列化时忽略null值
        objectMapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);

        return objectMapper;
    }

    /**
     * 创建RocketMQ专用的消息转换器
     */
    @Bean
    public RocketMQMessageConverter rocketMQMessageConverter() {
        RocketMQMessageConverter converter = new RocketMQMessageConverter();
        CompositeMessageConverter compositeMessageConverter = (CompositeMessageConverter) converter.getMessageConverter();
        List<MessageConverter> messageConverterList = compositeMessageConverter.getConverters();

        // 移除默认的Jackson转换器
        messageConverterList.removeIf(messageConverter -> messageConverter instanceof MappingJackson2MessageConverter);

        // 添加自定义的Jackson转换器
        MappingJackson2MessageConverter jackson2MessageConverter = new MappingJackson2MessageConverter();
        jackson2MessageConverter.setObjectMapper(objectMapper());
        messageConverterList.add(0, jackson2MessageConverter);

        return converter;
    }

    /**
     * 配置RocketMQ的Jackson ObjectMapper
     * 支持Java 8日期时间类型序列化
     */
    @PostConstruct
    public void configureRocketMQTemplate() {
        if (rocketMQTemplate != null) {
            // 创建Jackson消息转换器
            MappingJackson2MessageConverter converter = new MappingJackson2MessageConverter();
            converter.setObjectMapper(objectMapper());

            // 设置RocketMQ模板的消息转换器
            rocketMQTemplate.setMessageConverter(converter);
        }
    }
}
