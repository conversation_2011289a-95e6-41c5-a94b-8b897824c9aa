package com.kun.linkage.auth.config;

import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.PropertyAccessor;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import org.apache.rocketmq.spring.core.RocketMQTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.messaging.converter.MappingJackson2MessageConverter;

import javax.annotation.PostConstruct;
import java.util.TimeZone;

/**
 * RocketMQ配置类
 * <p>
 * 主要用于配置Jackson序列化器，支持Java 8日期时间类型
 */
@Configuration
public class RocketMqConfiguration {

    @Autowired
    private RocketMQTemplate rocketMQTemplate;

    /**
     * 配置RocketMQ的Jackson ObjectMapper
     * 支持Java 8日期时间类型序列化
     */
    @PostConstruct
    public void configureRocketMQTemplate() {
        ObjectMapper objectMapper = new ObjectMapper();

        // 注册JavaTimeModule，支持Java 8日期时间类型
        objectMapper.registerModule(new JavaTimeModule());

        // 设置可见性
        objectMapper.setVisibility(PropertyAccessor.ALL, JsonAutoDetect.Visibility.ANY);

        // 禁用将日期写为时间戳
        objectMapper.disable(com.fasterxml.jackson.databind.SerializationFeature.WRITE_DATES_AS_TIMESTAMPS);

        // 配置反序列化时忽略未知属性
        objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);

        // 启用对空对象的处理
        objectMapper.configure(DeserializationFeature.ACCEPT_EMPTY_STRING_AS_NULL_OBJECT, true);

        // 配置序列化时忽略null值
        objectMapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);

        // 创建Jackson消息转换器
        MappingJackson2MessageConverter converter = new MappingJackson2MessageConverter();
        converter.setObjectMapper(objectMapper);

        // 设置RocketMQ模板的消息转换器
        rocketMQTemplate.setMessageConverter(converter);
    }

    /**
     * 创建自定义的ObjectMapper Bean，确保在RocketMQ初始化前就可用
     */
    @Bean
    @Primary
    public ObjectMapper objectMapper() {
        ObjectMapper objectMapper = new ObjectMapper();

        // 注册JavaTimeModule
        objectMapper.registerModule(new JavaTimeModule());

        // 禁用时间戳格式
        objectMapper.disable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS);

        // 配置反序列化
        objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        objectMapper.configure(DeserializationFeature.ACCEPT_EMPTY_STRING_AS_NULL_OBJECT, true);

        // 设置可见性
        objectMapper.setVisibility(PropertyAccessor.ALL, JsonAutoDetect.Visibility.ANY);

        // 忽略null值
        objectMapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);

        return objectMapper;
    }

    /**
     * 创建RocketMQ专用的消息转换器
     */
    @Bean
    public MappingJackson2MessageConverter rocketMQMessageConverter() {
        MappingJackson2MessageConverter converter = new MappingJackson2MessageConverter();
        converter.setObjectMapper(objectMapper());
        return converter;
    }
}
